#!/usr/bin/env python3
"""
Setup script to make local solar calculator live on the internet
"""

import os
import sys
import subprocess
import time
import threading
import webbrowser

def print_banner():
    print("🌞 Solar Calculator - Live Local Setup")
    print("=" * 50)
    print("Making your local apps accessible from the internet!")
    print("=" * 50)

def check_ngrok():
    """Check if ngrok is installed"""
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ ngrok is already installed")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ ngrok not found")
    print("📥 Please install ngrok:")
    print("   1. Go to: https://ngrok.com/download")
    print("   2. Download ngrok for Windows")
    print("   3. Extract to a folder in your PATH")
    print("   4. Run: ngrok authtoken YOUR_TOKEN")
    return False

def start_flask_app():
    """Start the Flask app"""
    print("\n🚀 Starting Flask App...")
    
    # Set environment variables
    env = os.environ.copy()
    env['FORCE_LOCAL_MODE'] = 'true'
    env['FLASK_ENV'] = 'development'
    
    try:
        # Start Flask app in background
        process = subprocess.Popen(
            [sys.executable, 'solar_app.py'],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a bit for the app to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Flask app started on http://localhost:5000")
            return process
        else:
            print("❌ Failed to start Flask app")
            return None
            
    except Exception as e:
        print(f"❌ Error starting Flask app: {e}")
        return None

def start_streamlit_app():
    """Start the Streamlit app"""
    print("\n🚀 Starting Streamlit App...")
    
    try:
        # Start Streamlit app in background
        process = subprocess.Popen(
            [sys.executable, '-m', 'streamlit', 'run', 'internship-solar-energy/app.py', '--server.port', '8501'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a bit for the app to start
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Streamlit app started on http://localhost:8501")
            return process
        else:
            print("❌ Failed to start Streamlit app")
            return None
            
    except Exception as e:
        print(f"❌ Error starting Streamlit app: {e}")
        return None

def setup_ngrok_tunnels():
    """Setup ngrok tunnels for both apps"""
    print("\n🌐 Setting up ngrok tunnels...")
    
    try:
        # Start ngrok for Flask app (port 5000)
        flask_process = subprocess.Popen(
            ['ngrok', 'http', '5000'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        time.sleep(2)
        
        # Start ngrok for Streamlit app (port 8501)
        streamlit_process = subprocess.Popen(
            ['ngrok', 'http', '8501'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        time.sleep(3)
        
        print("✅ ngrok tunnels started!")
        print("\n📱 Your apps are now LIVE on the internet!")
        print("🔗 Check ngrok dashboard: http://localhost:4040")
        
        return flask_process, streamlit_process
        
    except Exception as e:
        print(f"❌ Error setting up ngrok: {e}")
        return None, None

def get_public_urls():
    """Get the public URLs from ngrok"""
    try:
        import requests
        response = requests.get('http://localhost:4040/api/tunnels')
        tunnels = response.json()['tunnels']
        
        urls = {}
        for tunnel in tunnels:
            port = tunnel['config']['addr'].split(':')[-1]
            public_url = tunnel['public_url']
            
            if port == '5000':
                urls['flask'] = public_url
            elif port == '8501':
                urls['streamlit'] = public_url
        
        return urls
    except:
        return {}

def main():
    print_banner()
    
    # Check if ngrok is installed
    if not check_ngrok():
        print("\n⚠️  Please install ngrok first, then run this script again.")
        return
    
    # Start Flask app
    flask_process = start_flask_app()
    if not flask_process:
        print("❌ Cannot proceed without Flask app")
        return
    
    # Start Streamlit app
    streamlit_process = start_streamlit_app()
    
    # Setup ngrok tunnels
    ngrok_flask, ngrok_streamlit = setup_ngrok_tunnels()
    
    if ngrok_flask or ngrok_streamlit:
        print("\n" + "=" * 50)
        print("🎉 SUCCESS! Your Solar Calculator is now LIVE!")
        print("=" * 50)
        
        # Wait a bit and try to get URLs
        time.sleep(5)
        urls = get_public_urls()
        
        if urls:
            if 'flask' in urls:
                print(f"🌐 Flask App (Main): {urls['flask']}")
            if 'streamlit' in urls:
                print(f"🌐 Streamlit App: {urls['streamlit']}")
        else:
            print("🌐 Check ngrok dashboard for URLs: http://localhost:4040")
        
        print("\n📋 Features Available:")
        print("   ✅ No monthly bill limits")
        print("   ✅ Industrial-scale calculations")
        print("   ✅ 100+ Indian cities")
        print("   ✅ PDF reports (mock mode)")
        print("   ✅ Multiple consumer types")
        
        print("\n🔗 Share these URLs with anyone!")
        print("⚠️  Keep this terminal open to maintain the connection")
        
        # Open ngrok dashboard
        webbrowser.open('http://localhost:4040')
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Shutting down...")
            
            # Cleanup processes
            if flask_process:
                flask_process.terminate()
            if streamlit_process:
                streamlit_process.terminate()
            if ngrok_flask:
                ngrok_flask.terminate()
            if ngrok_streamlit:
                ngrok_streamlit.terminate()
    
    else:
        print("❌ Failed to setup ngrok tunnels")

if __name__ == "__main__":
    main()
