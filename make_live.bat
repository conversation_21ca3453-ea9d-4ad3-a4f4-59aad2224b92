@echo off
echo 🌞 Solar Calculator - Make Live Locally
echo ================================================
echo Making your local apps accessible from the internet!
echo ================================================

echo.
echo 📋 What you need:
echo    1. ngrok installed (https://ngrok.com/download)
echo    2. ngrok auth token configured
echo.

pause

echo.
echo 🚀 Step 1: Starting Flask App...
set FORCE_LOCAL_MODE=true
set FLASK_ENV=development

start "Flask App" cmd /k "python solar_app.py"
timeout /t 5

echo.
echo 🚀 Step 2: Starting Streamlit App...
start "Streamlit App" cmd /k "cd internship-solar-energy && streamlit run app.py --server.port 8501"
timeout /t 5

echo.
echo 🌐 Step 3: Creating ngrok tunnels...
echo Opening ngrok for Flask app (port 5000)...
start "ngrok Flask" cmd /k "ngrok http 5000"
timeout /t 3

echo Opening ngrok for Streamlit app (port 8501)...
start "ngrok Streamlit" cmd /k "ngrok http 8501"
timeout /t 3

echo.
echo 🎉 SUCCESS! Your apps should now be LIVE!
echo ================================================
echo 📱 Local URLs:
echo    Flask App: http://localhost:5000
echo    Streamlit App: http://localhost:8501
echo.
echo 🌐 Public URLs:
echo    Check ngrok dashboard: http://localhost:4040
echo.
echo 🔗 Share the public URLs with anyone!
echo ⚠️  Keep all windows open to maintain connection
echo ================================================

start http://localhost:4040

pause
