#!/usr/bin/env python3
"""
Simple script to make Flask app live locally with external access
"""

import os
import sys
import socket
import subprocess
import time
import threading

def get_local_ip():
    """Get the local IP address"""
    try:
        # Connect to a remote server to get local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "localhost"

def start_flask_with_external_access():
    """Start Flask app with external access"""
    print("🌞 Solar Calculator - Live Local Setup")
    print("=" * 50)
    
    # Set environment variables
    os.environ['FORCE_LOCAL_MODE'] = 'true'
    os.environ['FLASK_ENV'] = 'development'
    
    # Get local IP
    local_ip = get_local_ip()
    
    print(f"🚀 Starting Flask App with external access...")
    print(f"📍 Local IP: {local_ip}")
    
    # Modify solar_app.py to run on all interfaces
    try:
        # Import and modify the Flask app
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # Import the app
        from solar_app import app
        
        print("\n🎉 Solar Calculator is now LIVE!")
        print("=" * 50)
        print(f"🌐 Local Access: http://localhost:5000")
        print(f"🌐 Network Access: http://{local_ip}:5000")
        print("=" * 50)
        print("📋 Features Available:")
        print("   ✅ No monthly bill limits")
        print("   ✅ Industrial-scale calculations") 
        print("   ✅ 100+ Indian cities")
        print("   ✅ PDF reports (mock mode)")
        print("   ✅ Multiple consumer types")
        print("=" * 50)
        print("🔗 Share the Network URL with others on your network!")
        print("⚠️  For internet access, use ngrok or similar service")
        print("💡 Run make_live.bat for internet access via ngrok")
        print("=" * 50)
        
        # Run the app with external access
        app.run(
            debug=True,
            host='0.0.0.0',  # Listen on all interfaces
            port=5000,
            use_reloader=False
        )
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("🔧 Try running: python solar_app.py")

if __name__ == "__main__":
    start_flask_with_external_access()
