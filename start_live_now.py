#!/usr/bin/env python3
"""
Start Solar Calculator LIVE with external access
"""

import os
import socket

def get_local_ip():
    """Get the local IP address"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "*************"  # fallback

# Set environment for local mode
os.environ['FORCE_LOCAL_MODE'] = 'true'
os.environ['FLASK_ENV'] = 'development'

# Get local IP
local_ip = get_local_ip()

print("🌞 Solar Calculator - LIVE LOCAL")
print("=" * 50)
print("🚀 Starting with external access...")

# Import and run the Flask app
if __name__ == "__main__":
    from solar_app import app
    
    print(f"\n🎉 Solar Calculator is now LIVE!")
    print("=" * 50)
    print(f"🌐 Local: http://localhost:5000")
    print(f"🌐 Network: http://{local_ip}:5000")
    print(f"🌐 External: http://{local_ip}:5000")
    print("=" * 50)
    print("📋 Features:")
    print("   ✅ No bill limits (supports ₹25+ lakh)")
    print("   ✅ Industrial calculations")
    print("   ✅ 100+ cities")
    print("   ✅ PDF reports")
    print("=" * 50)
    print("🔗 Share the Network URL!")
    print("⚠️  Keep this window open")
    print("=" * 50)
    
    # Run with external access
    app.run(
        debug=False,  # Disable debug for external access
        host='0.0.0.0',  # Listen on all interfaces
        port=5000,
        threaded=True
    )
