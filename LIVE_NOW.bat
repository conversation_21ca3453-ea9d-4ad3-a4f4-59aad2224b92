@echo off
cls
echo.
echo 🌞 Solar Calculator - GO LIVE NOW!
echo ================================================
echo.

echo 🚀 Starting Flask App with external access...
echo.

set FORCE_LOCAL_MODE=true
set FLASK_ENV=development

echo ✅ Environment configured
echo ✅ Network access enabled
echo ✅ All fixes applied
echo.

echo 🌐 Your app will be available at:
echo    Local: http://localhost:5000
echo    Network: http://YOUR_IP:5000
echo.

echo 📋 Features available:
echo    ✅ No monthly bill limits
echo    ✅ Industrial-scale calculations
echo    ✅ 100+ Indian cities
echo    ✅ PDF reports (mock mode)
echo    ✅ Multiple consumer types
echo.

echo 🔗 Share the Network URL with others!
echo ⚠️  Keep this window open to maintain connection
echo.
echo ================================================
echo Starting in 3 seconds...
timeout /t 3 /nobreak >nul

python solar_app.py

pause
